package main

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/gorilla/mux"
)

// 🎯 KANBAN HANDLERS - Amazing Complex Kanban System

// ==========================================
// BOARD HANDLERS
// ==========================================

func handleListKanbanBoards(w http.ResponseWriter, r *http.Request) {
	// Mock data for now - will be replaced with real database operations
	boards := []map[string]interface{}{
		{
			"id":          1,
			"name":        "Sales Pipeline",
			"description": "Complete sales workflow from lead to customer",
			"board_type":  "sales_pipeline",
			"color":       "#3498db",
			"icon":        "🎯",
			"is_active":   true,
			"created_at":  time.Now().Add(-30 * 24 * time.Hour),
			"card_count":  23,
			"columns": []map[string]interface{}{
				{"id": 1, "name": "New Lead", "card_count": 8, "color": "#3498db"},
				{"id": 2, "name": "Qualification", "card_count": 5, "color": "#9b59b6"},
				{"id": 3, "name": "Proposal", "card_count": 4, "color": "#f39c12"},
				{"id": 4, "name": "Negotiation", "card_count": 3, "color": "#e67e22"},
				{"id": 5, "name": "Completed", "card_count": 3, "color": "#27ae60"},
			},
		},
		{
			"id":          2,
			"name":        "Service Orders",
			"description": "Service request workflow management",
			"board_type":  "service_orders",
			"color":       "#e74c3c",
			"icon":        "🔧",
			"is_active":   true,
			"created_at":  time.Now().Add(-15 * 24 * time.Hour),
			"card_count":  18,
			"columns": []map[string]interface{}{
				{"id": 6, "name": "New Request", "card_count": 6, "color": "#3498db"},
				{"id": 7, "name": "Scheduled", "card_count": 7, "color": "#f39c12"},
				{"id": 8, "name": "In Progress", "card_count": 3, "color": "#e67e22"},
				{"id": 9, "name": "Completed", "card_count": 2, "color": "#27ae60"},
			},
		},
		{
			"id":          3,
			"name":        "Customer Lifecycle",
			"description": "Track customer journey and engagement",
			"board_type":  "customer_lifecycle",
			"color":       "#2ecc71",
			"icon":        "👥",
			"is_active":   true,
			"created_at":  time.Now().Add(-7 * 24 * time.Hour),
			"card_count":  156,
			"columns": []map[string]interface{}{
				{"id": 10, "name": "Lead", "card_count": 23, "color": "#3498db"},
				{"id": 11, "name": "Prospect", "card_count": 15, "color": "#9b59b6"},
				{"id": 12, "name": "New Customer", "card_count": 12, "color": "#f39c12"},
				{"id": 13, "name": "Regular Customer", "card_count": 89, "color": "#2ecc71"},
				{"id": 14, "name": "VIP Customer", "card_count": 17, "color": "#e67e22"},
			},
		},
	}

	response := UnifiedCRMResponse{
		Data: boards,
		Meta: ResponseMeta{
			Total:         len(boards),
			QueryTime:     "15ms",
			DataFreshness: "real-time",
		},
		UI: UIEnhancements{
			Actions: []QuickAction{
				{ID: "new_board", Label: "New Board", Icon: "➕", Color: "#3498db", Endpoint: "/api/kanban/boards", Method: "POST"},
				{ID: "board_templates", Label: "Templates", Icon: "📋", Color: "#9b59b6", Endpoint: "/api/kanban/templates", Method: "GET"},
			},
		},
		Context: map[string]interface{}{
			"total_cards":    197,
			"active_boards":  3,
			"workflow_types": []string{"sales_pipeline", "service_orders", "customer_lifecycle", "equipment_maintenance"},
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func handleGetKanbanBoard(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	boardID := vars["id"]

	// Mock detailed board data
	board := map[string]interface{}{
		"id":          1,
		"name":        "Sales Pipeline",
		"description": "Complete sales workflow from lead to customer",
		"board_type":  "sales_pipeline",
		"color":       "#3498db",
		"icon":        "🎯",
		"is_active":   true,
		"created_at":  time.Now().Add(-30 * 24 * time.Hour),
		"columns": []map[string]interface{}{
			{
				"id":           1,
				"name":         "New Lead",
				"description":  "Newly acquired leads",
				"position":     1,
				"color":        "#3498db",
				"icon":         "🎯",
				"wip_limit":    0,
				"is_completed": false,
				"cards": []map[string]interface{}{
					{
						"id":              1,
						"title":           "Anna Kowalska - Klimatyzacja biuro",
						"description":     "Zapytanie o klimatyzację do biura 50m²",
						"card_type":       "lead",
						"priority":        "high",
						"color":           "#ffffff",
						"tags":            []string{"klimatyzacja", "biuro", "warszawa"},
						"entity_id":       123,
						"entity_type":     "lead",
						"estimated_value": 15000.00,
						"due_date":        time.Now().Add(3 * 24 * time.Hour),
						"assigned_to":     1,
						"created_at":      time.Now().Add(-2 * time.Hour),
						"priority_color":  "#fd7e14",
						"priority_icon":   "🔴",
						"days_in_column":  0,
						"is_overdue":      false,
					},
					{
						"id":              2,
						"title":           "Marek Nowak - Pompa ciepła",
						"description":     "Zainteresowany pompą ciepła do domu 120m²",
						"card_type":       "lead",
						"priority":        "normal",
						"color":           "#ffffff",
						"tags":            []string{"pompa-ciepła", "dom", "kraków"},
						"entity_id":       124,
						"entity_type":     "lead",
						"estimated_value": 35000.00,
						"due_date":        time.Now().Add(5 * 24 * time.Hour),
						"assigned_to":     2,
						"created_at":      time.Now().Add(-4 * time.Hour),
						"priority_color":  "#3498db",
						"priority_icon":   "🔵",
						"days_in_column":  0,
						"is_overdue":      false,
					},
				},
			},
			{
				"id":           2,
				"name":         "Qualification",
				"description":  "Qualifying lead requirements",
				"position":     2,
				"color":        "#9b59b6",
				"icon":         "🔍",
				"wip_limit":    10,
				"is_completed": false,
				"cards": []map[string]interface{}{
					{
						"id":              3,
						"title":           "Firma ABC - System wentylacji",
						"description":     "Kompleksowy system wentylacji dla biurowca",
						"card_type":       "opportunity",
						"priority":        "urgent",
						"color":           "#ffffff",
						"tags":            []string{"wentylacja", "biurowiec", "gdańsk"},
						"entity_id":       125,
						"entity_type":     "opportunity",
						"estimated_value": 85000.00,
						"due_date":        time.Now().Add(1 * 24 * time.Hour),
						"assigned_to":     1,
						"created_at":      time.Now().Add(-3 * 24 * time.Hour),
						"priority_color":  "#f39c12",
						"priority_icon":   "⚡",
						"days_in_column":  3,
						"is_overdue":      false,
					},
				},
			},
			{
				"id":           3,
				"name":         "Proposal",
				"description":  "Preparing and sending proposals",
				"position":     3,
				"color":        "#f39c12",
				"icon":         "📋",
				"wip_limit":    5,
				"is_completed": false,
				"cards": []map[string]interface{}{
					{
						"id":              4,
						"title":           "Hotel XYZ - Klimatyzacja pokoi",
						"description":     "Klimatyzacja dla 50 pokoi hotelowych",
						"card_type":       "project",
						"priority":        "high",
						"color":           "#ffffff",
						"tags":            []string{"klimatyzacja", "hotel", "wrocław"},
						"entity_id":       126,
						"entity_type":     "project",
						"estimated_value": 120000.00,
						"due_date":        time.Now().Add(7 * 24 * time.Hour),
						"assigned_to":     3,
						"created_at":      time.Now().Add(-5 * 24 * time.Hour),
						"priority_color":  "#fd7e14",
						"priority_icon":   "🔴",
						"days_in_column":  5,
						"is_overdue":      false,
					},
				},
			},
		},
		"analytics": map[string]interface{}{
			"total_cards":     23,
			"overdue_cards":   0,
			"avg_cycle_time":  "5.2 days",
			"completion_rate": "87%",
			"total_value":     "1,250,000 PLN",
		},
	}

	response := UnifiedCRMResponse{
		Data: board,
		Meta: ResponseMeta{
			QueryTime:     "35ms",
			DataFreshness: "real-time",
		},
		UI: UIEnhancements{
			Actions: []QuickAction{
				{ID: "new_card", Label: "New Card", Icon: "➕", Color: "#27ae60", Endpoint: "/api/kanban/cards", Method: "POST"},
				{ID: "edit_board", Label: "Edit Board", Icon: "✏️", Color: "#3498db", Endpoint: "/api/kanban/boards/" + boardID, Method: "PUT"},
				{ID: "board_analytics", Label: "Analytics", Icon: "📊", Color: "#9b59b6", Endpoint: "/api/kanban/boards/" + boardID + "/analytics", Method: "GET"},
			},
		},
		Context: map[string]interface{}{
			"board_id":       boardID,
			"can_edit":       true,
			"can_add_cards":  true,
			"workflow_stage": "active",
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func handleCreateKanbanBoard(w http.ResponseWriter, r *http.Request) {
	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Mock board creation
	board := map[string]interface{}{
		"id":          4,
		"name":        req["name"],
		"description": req["description"],
		"board_type":  req["board_type"],
		"color":       req["color"],
		"icon":        req["icon"],
		"is_active":   true,
		"created_at":  time.Now(),
		"created_by":  1,
	}

	response := UnifiedCRMResponse{
		Data: board,
		Meta: ResponseMeta{
			QueryTime:     "25ms",
			DataFreshness: "real-time",
		},
		Context: map[string]interface{}{
			"action":  "created",
			"success": true,
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(response)
}

func handleUpdateKanbanBoard(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	boardID := vars["id"]

	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Mock board update
	board := map[string]interface{}{
		"id":          boardID,
		"name":        req["name"],
		"description": req["description"],
		"color":       req["color"],
		"icon":        req["icon"],
		"updated_at":  time.Now(),
	}

	response := UnifiedCRMResponse{
		Data: board,
		Meta: ResponseMeta{
			QueryTime:     "18ms",
			DataFreshness: "real-time",
		},
		Context: map[string]interface{}{
			"action":   "updated",
			"board_id": boardID,
			"success":  true,
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func handleDeleteKanbanBoard(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	boardID := vars["id"]

	response := UnifiedCRMResponse{
		Data: map[string]interface{}{
			"deleted":    true,
			"board_id":   boardID,
			"deleted_at": time.Now(),
		},
		Meta: ResponseMeta{
			QueryTime:     "12ms",
			DataFreshness: "real-time",
		},
		Context: map[string]interface{}{
			"action":   "deleted",
			"board_id": boardID,
			"success":  true,
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func handleKanbanBoardAnalytics(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	boardID := vars["id"]

	analytics := map[string]interface{}{
		"board_id":        boardID,
		"total_cards":     23,
		"overdue_cards":   2,
		"completed_cards": 8,
		"avg_cycle_time":  "5.2 days",
		"completion_rate": 87.5,
		"total_value":     1250000.00,
		"formatted_value": "1,250,000 PLN",
		"column_stats": []map[string]interface{}{
			{"column": "New Lead", "cards": 8, "avg_time": "1.2 days", "conversion_rate": 75.0},
			{"column": "Qualification", "cards": 5, "avg_time": "2.1 days", "conversion_rate": 80.0},
			{"column": "Proposal", "cards": 4, "avg_time": "3.5 days", "conversion_rate": 65.0},
			{"column": "Negotiation", "cards": 3, "avg_time": "4.2 days", "conversion_rate": 90.0},
			{"column": "Completed", "cards": 3, "avg_time": "0 days", "conversion_rate": 100.0},
		},
		"priority_distribution": map[string]int{
			"critical": 1,
			"urgent":   3,
			"high":     7,
			"normal":   10,
			"low":      2,
		},
		"insights": []string{
			"🎯 Conversion rate from Proposal to Negotiation is below average (65%)",
			"⚡ 2 cards are overdue and need immediate attention",
			"📈 Overall pipeline health is good with 87.5% completion rate",
			"🔄 Consider reviewing cards in Qualification stage (avg 2.1 days)",
		},
		"trends": map[string]interface{}{
			"cards_created_this_week":   12,
			"cards_completed_this_week": 8,
			"velocity_trend":            "increasing",
			"bottleneck_column":         "Proposal",
		},
	}

	response := UnifiedCRMResponse{
		Data: analytics,
		Meta: ResponseMeta{
			QueryTime:     "45ms",
			DataFreshness: "real-time",
		},
		Context: map[string]interface{}{
			"board_id":      boardID,
			"analysis_type": "comprehensive",
			"period":        "last_30_days",
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// ==========================================
// COLUMN HANDLERS
// ==========================================

func handleCreateKanbanColumn(w http.ResponseWriter, r *http.Request) {
	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	column := map[string]interface{}{
		"id":           5,
		"board_id":     req["board_id"],
		"name":         req["name"],
		"description":  req["description"],
		"position":     req["position"],
		"color":        req["color"],
		"icon":         req["icon"],
		"wip_limit":    req["wip_limit"],
		"is_completed": false,
		"created_at":   time.Now(),
	}

	response := UnifiedCRMResponse{
		Data:      column,
		Meta:      ResponseMeta{QueryTime: "15ms", DataFreshness: "real-time"},
		Context:   map[string]interface{}{"action": "created", "success": true},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(response)
}

func handleUpdateKanbanColumn(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	columnID := vars["id"]

	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	column := map[string]interface{}{
		"id":          columnID,
		"name":        req["name"],
		"description": req["description"],
		"color":       req["color"],
		"icon":        req["icon"],
		"wip_limit":   req["wip_limit"],
		"updated_at":  time.Now(),
	}

	response := UnifiedCRMResponse{
		Data:      column,
		Meta:      ResponseMeta{QueryTime: "12ms", DataFreshness: "real-time"},
		Context:   map[string]interface{}{"action": "updated", "column_id": columnID, "success": true},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func handleDeleteKanbanColumn(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	columnID := vars["id"]

	response := UnifiedCRMResponse{
		Data: map[string]interface{}{
			"deleted":    true,
			"column_id":  columnID,
			"deleted_at": time.Now(),
		},
		Meta:      ResponseMeta{QueryTime: "8ms", DataFreshness: "real-time"},
		Context:   map[string]interface{}{"action": "deleted", "column_id": columnID, "success": true},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// ==========================================
// CARD HANDLERS
// ==========================================

func handleCreateKanbanCard(w http.ResponseWriter, r *http.Request) {
	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	card := map[string]interface{}{
		"id":              10,
		"board_id":        req["board_id"],
		"column_id":       req["column_id"],
		"title":           req["title"],
		"description":     req["description"],
		"card_type":       req["card_type"],
		"priority":        req["priority"],
		"color":           req["color"],
		"tags":            req["tags"],
		"entity_id":       req["entity_id"],
		"entity_type":     req["entity_type"],
		"estimated_hours": req["estimated_hours"],
		"estimated_value": req["estimated_value"],
		"due_date":        req["due_date"],
		"assigned_to":     req["assigned_to"],
		"created_at":      time.Now(),
		"created_by":      1,
		"position":        1,
		"priority_color":  "#3498db",
		"priority_icon":   "🔵",
		"days_in_column":  0,
		"is_overdue":      false,
	}

	response := UnifiedCRMResponse{
		Data:      card,
		Meta:      ResponseMeta{QueryTime: "20ms", DataFreshness: "real-time"},
		Context:   map[string]interface{}{"action": "created", "success": true},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(response)
}

func handleGetKanbanCard(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	cardID := vars["id"]

	card := map[string]interface{}{
		"id":                  cardID,
		"board_id":            1,
		"column_id":           1,
		"title":               "Anna Kowalska - Klimatyzacja biuro",
		"description":         "Zapytanie o klimatyzację do biura 50m². Klient potrzebuje szybkiej instalacji przed sezonem letnim.",
		"card_type":           "lead",
		"priority":            "high",
		"color":               "#ffffff",
		"tags":                []string{"klimatyzacja", "biuro", "warszawa", "pilne"},
		"entity_id":           123,
		"entity_type":         "lead",
		"estimated_hours":     8.0,
		"actual_hours":        2.5,
		"estimated_value":     15000.00,
		"due_date":            time.Now().Add(3 * 24 * time.Hour),
		"assigned_to":         1,
		"assigned_team":       []string{"sales", "technical"},
		"created_at":          time.Now().Add(-2 * time.Hour),
		"updated_at":          time.Now().Add(-30 * time.Minute),
		"created_by":          1,
		"position":            1,
		"priority_color":      "#fd7e14",
		"priority_icon":       "🔴",
		"days_in_column":      0,
		"is_overdue":          false,
		"progress_percentage": 31.25,
		"activities": []map[string]interface{}{
			{
				"id":            1,
				"activity_type": "created",
				"description":   "Card 'Anna Kowalska - Klimatyzacja biuro' was created",
				"created_by":    1,
				"created_at":    time.Now().Add(-2 * time.Hour),
			},
			{
				"id":            2,
				"activity_type": "assigned",
				"description":   "Card assigned to Marek Kowalski",
				"created_by":    1,
				"created_at":    time.Now().Add(-1 * time.Hour),
			},
		},
		"comments": []map[string]interface{}{
			{
				"id":         1,
				"content":    "Klient bardzo zainteresowany, prosi o szybką wycenę",
				"created_by": 1,
				"created_at": time.Now().Add(-30 * time.Minute),
			},
		},
		"attachments": []map[string]interface{}{
			{
				"id":            1,
				"filename":      "plan_biura.pdf",
				"original_name": "Plan biura - Anna Kowalska.pdf",
				"content_type":  "application/pdf",
				"size":          1024000,
				"url":           "/api/files/attachments/1",
				"created_at":    time.Now().Add(-1 * time.Hour),
			},
		},
	}

	response := UnifiedCRMResponse{
		Data: card,
		Meta: ResponseMeta{QueryTime: "25ms", DataFreshness: "real-time"},
		UI: UIEnhancements{
			Actions: []QuickAction{
				{ID: "edit_card", Label: "Edit", Icon: "✏️", Color: "#3498db"},
				{ID: "move_card", Label: "Move", Icon: "🔄", Color: "#9b59b6"},
				{ID: "add_comment", Label: "Comment", Icon: "💬", Color: "#27ae60"},
				{ID: "attach_file", Label: "Attach", Icon: "📎", Color: "#f39c12"},
			},
		},
		Context: map[string]interface{}{
			"card_id":    cardID,
			"can_edit":   true,
			"can_move":   true,
			"can_delete": true,
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func handleUpdateKanbanCard(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	cardID := vars["id"]

	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	card := map[string]interface{}{
		"id":              cardID,
		"title":           req["title"],
		"description":     req["description"],
		"priority":        req["priority"],
		"due_date":        req["due_date"],
		"assigned_to":     req["assigned_to"],
		"estimated_hours": req["estimated_hours"],
		"actual_hours":    req["actual_hours"],
		"updated_at":      time.Now(),
	}

	response := UnifiedCRMResponse{
		Data:      card,
		Meta:      ResponseMeta{QueryTime: "18ms", DataFreshness: "real-time"},
		Context:   map[string]interface{}{"action": "updated", "card_id": cardID, "success": true},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func handleDeleteKanbanCard(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	cardID := vars["id"]

	response := UnifiedCRMResponse{
		Data: map[string]interface{}{
			"deleted":    true,
			"card_id":    cardID,
			"deleted_at": time.Now(),
		},
		Meta:      ResponseMeta{QueryTime: "10ms", DataFreshness: "real-time"},
		Context:   map[string]interface{}{"action": "deleted", "card_id": cardID, "success": true},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func handleMoveKanbanCard(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	cardID := vars["id"]

	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	response := UnifiedCRMResponse{
		Data: map[string]interface{}{
			"card_id":       cardID,
			"old_column_id": req["old_column_id"],
			"new_column_id": req["new_column_id"],
			"new_position":  req["new_position"],
			"moved_at":      time.Now(),
			"moved_by":      req["user_id"],
		},
		Meta:      ResponseMeta{QueryTime: "22ms", DataFreshness: "real-time"},
		Context:   map[string]interface{}{"action": "moved", "card_id": cardID, "success": true},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func handleCreateKanbanComment(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	cardID := vars["id"]

	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	comment := map[string]interface{}{
		"id":         5,
		"card_id":    cardID,
		"content":    req["content"],
		"created_by": req["created_by"],
		"created_at": time.Now(),
	}

	response := UnifiedCRMResponse{
		Data:      comment,
		Meta:      ResponseMeta{QueryTime: "15ms", DataFreshness: "real-time"},
		Context:   map[string]interface{}{"action": "comment_created", "card_id": cardID, "success": true},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(response)
}

func handleGetKanbanCardActivities(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	cardID := vars["id"]

	activities := []map[string]interface{}{
		{
			"id":            1,
			"card_id":       cardID,
			"activity_type": "created",
			"description":   "Card was created",
			"created_by":    1,
			"created_at":    time.Now().Add(-2 * time.Hour),
		},
		{
			"id":            2,
			"card_id":       cardID,
			"activity_type": "assigned",
			"description":   "Card assigned to Marek Kowalski",
			"old_value":     "",
			"new_value":     "Marek Kowalski",
			"created_by":    1,
			"created_at":    time.Now().Add(-1 * time.Hour),
		},
		{
			"id":            3,
			"card_id":       cardID,
			"activity_type": "commented",
			"description":   "Added a comment",
			"created_by":    1,
			"created_at":    time.Now().Add(-30 * time.Minute),
		},
	}

	response := UnifiedCRMResponse{
		Data: activities,
		Meta: ResponseMeta{
			Total:         len(activities),
			QueryTime:     "12ms",
			DataFreshness: "real-time",
		},
		Context:   map[string]interface{}{"card_id": cardID},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// ==========================================
// AUTOMATION & TEMPLATES
// ==========================================

func handleAutoMoveCard(w http.ResponseWriter, r *http.Request) {
	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	response := UnifiedCRMResponse{
		Data: map[string]interface{}{
			"card_id":       req["card_id"],
			"trigger":       req["trigger"],
			"auto_moved":    true,
			"new_column_id": 3,
			"reason":        "Automated workflow rule: Lead qualified after 2 interactions",
			"moved_at":      time.Now(),
		},
		Meta: ResponseMeta{QueryTime: "35ms", DataFreshness: "real-time"},
		Context: map[string]interface{}{
			"action":     "auto_moved",
			"automation": true,
			"success":    true,
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func handleGetKanbanTemplates(w http.ResponseWriter, r *http.Request) {
	templates := []map[string]interface{}{
		{
			"id":          "sales_pipeline",
			"name":        "Sales Pipeline",
			"description": "Complete sales workflow from lead to customer",
			"icon":        "🎯",
			"color":       "#3498db",
			"columns": []map[string]interface{}{
				{"name": "New Lead", "color": "#3498db", "icon": "🎯", "wip_limit": 0},
				{"name": "Qualification", "color": "#9b59b6", "icon": "🔍", "wip_limit": 10},
				{"name": "Proposal", "color": "#f39c12", "icon": "📋", "wip_limit": 5},
				{"name": "Negotiation", "color": "#e67e22", "icon": "🤝", "wip_limit": 3},
				{"name": "Scheduling", "color": "#2ecc71", "icon": "📅", "wip_limit": 8},
				{"name": "Execution", "color": "#1abc9c", "icon": "🔧", "wip_limit": 15},
				{"name": "Completed", "color": "#27ae60", "icon": "✅", "is_completed": true},
				{"name": "Lost", "color": "#e74c3c", "icon": "❌", "is_completed": true},
			},
		},
		{
			"id":          "service_orders",
			"name":        "Service Orders",
			"description": "Service request workflow management",
			"icon":        "🔧",
			"color":       "#e74c3c",
			"columns": []map[string]interface{}{
				{"name": "New Request", "color": "#3498db", "icon": "📞", "wip_limit": 0},
				{"name": "Assessment", "color": "#9b59b6", "icon": "🔍", "wip_limit": 5},
				{"name": "Scheduled", "color": "#f39c12", "icon": "📅", "wip_limit": 20},
				{"name": "In Progress", "color": "#e67e22", "icon": "🔧", "wip_limit": 10},
				{"name": "Quality Check", "color": "#2ecc71", "icon": "✔️", "wip_limit": 5},
				{"name": "Completed", "color": "#27ae60", "icon": "✅", "is_completed": true},
				{"name": "Invoiced", "color": "#1abc9c", "icon": "💰", "is_completed": true},
			},
		},
		{
			"id":          "customer_lifecycle",
			"name":        "Customer Lifecycle",
			"description": "Track customer journey and engagement",
			"icon":        "👥",
			"color":       "#2ecc71",
			"columns": []map[string]interface{}{
				{"name": "Lead", "color": "#3498db", "icon": "🎯", "wip_limit": 0},
				{"name": "Prospect", "color": "#9b59b6", "icon": "👤", "wip_limit": 0},
				{"name": "New Customer", "color": "#f39c12", "icon": "🆕", "wip_limit": 0},
				{"name": "Regular Customer", "color": "#2ecc71", "icon": "👥", "wip_limit": 0},
				{"name": "VIP Customer", "color": "#e67e22", "icon": "👑", "wip_limit": 0},
				{"name": "At Risk", "color": "#e74c3c", "icon": "⚠️", "wip_limit": 0},
				{"name": "Churned", "color": "#95a5a6", "icon": "❌", "is_completed": true},
			},
		},
		{
			"id":          "equipment_maintenance",
			"name":        "Equipment Maintenance",
			"description": "Preventive maintenance workflow",
			"icon":        "⚙️",
			"color":       "#9b59b6",
			"columns": []map[string]interface{}{
				{"name": "Due", "color": "#f39c12", "icon": "⏰", "wip_limit": 0},
				{"name": "Scheduled", "color": "#3498db", "icon": "📅", "wip_limit": 20},
				{"name": "In Progress", "color": "#e67e22", "icon": "🔧", "wip_limit": 10},
				{"name": "Completed", "color": "#27ae60", "icon": "✅", "is_completed": true},
				{"name": "Next Service", "color": "#1abc9c", "icon": "🔄", "is_completed": true},
			},
		},
	}

	response := UnifiedCRMResponse{
		Data: templates,
		Meta: ResponseMeta{
			Total:         len(templates),
			QueryTime:     "8ms",
			DataFreshness: "static",
		},
		UI: UIEnhancements{
			Actions: []QuickAction{
				{ID: "create_from_template", Label: "Use Template", Icon: "🚀", Color: "#27ae60"},
				{ID: "customize_template", Label: "Customize", Icon: "⚙️", Color: "#3498db"},
			},
		},
		Context: map[string]interface{}{
			"template_count": len(templates),
			"categories":     []string{"sales", "service", "customer", "maintenance"},
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}
